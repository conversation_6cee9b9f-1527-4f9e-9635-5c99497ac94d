/**
 * PulseChain News Hub Server
 * Main Express.js server with REST API endpoints
 */

require('dotenv').config();
const express = require('express');
const cors = require('cors');
const path = require('path');
const cron = require('node-cron');
const WebSocket = require('ws');

// Import services
const database = require('./src/database/database');
const deepseek = require('./src/services/deepseek');
const scraper = require('./src/services/scraper');
const dexService = require('./src/services/dex');
const analyzer = require('./src/services/analyzer');

const app = express();
const PORT = process.env.PORT || 3001;
const WS_PORT = process.env.WS_PORT || 3002;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, 'public')));
app.use('/css', express.static(path.join(__dirname, 'css')));
app.use('/js', express.static(path.join(__dirname, 'js')));
app.use('/pages', express.static(path.join(__dirname, 'pages')));

// Rate limiting
const rateLimit = require('express-rate-limit');
const limiter = rateLimit({
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW) || 15 * 60 * 1000, // 15 minutes
    max: parseInt(process.env.RATE_LIMIT_MAX) || 100 // limit each IP to 100 requests per windowMs
});
app.use('/api/', limiter);

// ========== MIDDLEWARE ==========

// Request logging middleware
app.use((req, res, next) => {
    console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
    next();
});

// ========== API ROUTES ==========

// Health check endpoint
app.get('/api/health', async (req, res) => {
    try {
        const [dbTest, deepseekTest] = await Promise.allSettled([
            database.testConnection(),
            deepseek.testConnection()
        ]);

        const health = {
            status: 'healthy',
            timestamp: new Date().toISOString(),
            services: {
                database: dbTest.status === 'fulfilled' ? dbTest.value : { success: false, error: dbTest.reason?.message },
                deepseek: deepseekTest.status === 'fulfilled' ? deepseekTest.value : { success: false, error: deepseekTest.reason?.message },
                analyzer: analyzer.getStatus()
            },
            uptime: process.uptime(),
            memory: process.memoryUsage(),
            version: '1.0.0'
        };

        const allHealthy = health.services.database.success && health.services.deepseek.success;
        res.status(allHealthy ? 200 : 503).json(health);

    } catch (error) {
        res.status(500).json({
            status: 'error',
            message: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

// ========== NEWS ENDPOINTS ==========

// Get recent articles with filtering
app.get('/api/articles', async (req, res) => {
    try {
        const {
            limit = 50,
            offset = 0,
            sentiment,
            timeframe = '24h',
            minCredibility = 5.0,
            source
        } = req.query;

        const filters = {};
        if (sentiment) filters.sentiment = sentiment;
        if (minCredibility) filters.minCredibility = parseFloat(minCredibility);
        if (timeframe) filters.timeframe = timeframe;
        if (source) filters.source = source;

        const articles = await database.getRecentArticles(
            parseInt(limit),
            parseInt(offset),
            filters
        );

        // Get total count for pagination
        const totalResult = await database.get(`
            SELECT COUNT(*) as total FROM articles a
            JOIN sources s ON a.source_id = s.id
            WHERE a.published_at > datetime('now', '-24 hours')
        `);

        res.json({
            success: true,
            data: articles,
            pagination: {
                total: totalResult.total,
                limit: parseInt(limit),
                offset: parseInt(offset),
                hasMore: (parseInt(offset) + articles.length) < totalResult.total
            },
            filters: filters,
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        console.error('Articles fetch error:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Get article by ID with full analysis
app.get('/api/articles/:id', async (req, res) => {
    try {
        const article = await database.get(`
            SELECT a.*, s.name as source_name, s.credibility_score as source_credibility
            FROM articles a
            JOIN sources s ON a.source_id = s.id
            WHERE a.id = ?
        `, [req.params.id]);

        if (!article) {
            return res.status(404).json({
                success: false,
                message: 'Article not found'
            });
        }

        // Get related articles
        const relatedArticles = await database.all(`
            SELECT id, title, sentiment_score, published_at
            FROM articles 
            WHERE id != ? AND sentiment_label = ?
            ORDER BY published_at DESC
            LIMIT 5
        `, [article.id, article.sentiment_label]);

        res.json({
            success: true,
            data: {
                ...article,
                key_insights: article.key_insights ? JSON.parse(article.key_insights) : [],
                manipulation_flags: article.manipulation_flags ? JSON.parse(article.manipulation_flags) : {},
                related_articles: relatedArticles
            }
        });

    } catch (error) {
        console.error('Article fetch error:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Get articles by sentiment
app.get('/api/articles/sentiment/:sentiment', async (req, res) => {
    try {
        const articles = await database.getArticlesBySentiment(req.params.sentiment);

        res.json({
            success: true,
            data: articles,
            count: articles.length,
            sentiment: req.params.sentiment
        });

    } catch (error) {
        console.error('Sentiment articles error:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Search articles with query and filters
app.get('/api/articles/search', async (req, res) => {
    try {
        const {
            q = '', // search query
            limit = 20,
            offset = 0,
            sentiment,
            timeframe = '7d',
            minCredibility = 0,
            source
        } = req.query;

        // Build search conditions
        const conditions = [];
        const params = [];

        // Text search in title and description
        if (q.trim()) {
            conditions.push('(a.title LIKE ? OR a.description LIKE ? OR a.content LIKE ?)');
            const searchTerm = `%${q.trim()}%`;
            params.push(searchTerm, searchTerm, searchTerm);
        }

        // Sentiment filter
        if (sentiment) {
            conditions.push('a.sentiment_label = ?');
            params.push(sentiment);
        }

        // Source filter
        if (source) {
            conditions.push('s.name = ?');
            params.push(source);
        }

        // Credibility filter
        if (minCredibility > 0) {
            conditions.push('s.credibility_score >= ?');
            params.push(parseFloat(minCredibility));
        }

        // Timeframe filter
        let timeCondition = '';
        if (timeframe === '24h') {
            timeCondition = "a.published_at > datetime('now', '-1 day')";
        } else if (timeframe === '7d') {
            timeCondition = "a.published_at > datetime('now', '-7 days')";
        } else if (timeframe === '30d') {
            timeCondition = "a.published_at > datetime('now', '-30 days')";
        }
        
        if (timeCondition) {
            conditions.push(timeCondition);
        }

        // Build final query
        const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';
        
        const sql = `
            SELECT 
                a.*,
                s.name as source_name,
                s.credibility_score as source_credibility,
                s.tags as source_tags
            FROM articles a
            JOIN sources s ON a.source_id = s.id
            ${whereClause}
            ORDER BY a.published_at DESC
            LIMIT ? OFFSET ?
        `;

        params.push(parseInt(limit), parseInt(offset));

        const articles = await database.all(sql, params);

        // Get total count for the same search
        const countSql = `
            SELECT COUNT(*) as total
            FROM articles a
            JOIN sources s ON a.source_id = s.id
            ${whereClause}
        `;

        const countParams = params.slice(0, -2); // Remove limit and offset
        const totalResult = await database.get(countSql, countParams);

        res.json({
            success: true,
            data: articles,
            pagination: {
                total: totalResult.total,
                limit: parseInt(limit),
                offset: parseInt(offset),
                hasMore: totalResult.total > parseInt(offset) + articles.length
            },
            query: q,
            filters: { sentiment, timeframe, minCredibility, source }
        });

    } catch (error) {
        console.error('Search articles error:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// ========== DEX DATA ENDPOINTS ==========

// Get current DEX data
app.get('/api/dex/current', async (req, res) => {
    try {
        const dexData = await dexService.getDexData();

        res.json({
            success: true,
            data: dexData,
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        console.error('DEX data error:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Get enhanced DEX data with AI analysis
app.get('/api/dex/enhanced', async (req, res) => {
    try {
        // Get recent news for context
        const recentNews = await database.getRecentArticles(10, 0, { timeframe: '24h' });
        const enhancedData = await dexService.getEnhancedDexData(recentNews);

        res.json({
            success: true,
            data: enhancedData,
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        console.error('Enhanced DEX data error:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Get price history
app.get('/api/dex/history/:token', async (req, res) => {
    try {
        const { token } = req.params;
        const { hours = 24 } = req.query;

        const history = await database.getPriceHistory(token.toUpperCase(), parseInt(hours));

        res.json({
            success: true,
            data: history,
            token: token.toUpperCase(),
            hours: parseInt(hours)
        });

    } catch (error) {
        console.error('Price history error:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// ========== TRADING SIGNALS ENDPOINTS ==========

// Get active trading signals
app.get('/api/signals', async (req, res) => {
    try {
        const signals = await database.getActiveTradingSignals();

        res.json({
            success: true,
            data: signals,
            count: signals.length,
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        console.error('Trading signals error:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Update signal performance
app.post('/api/signals/:id/performance', async (req, res) => {
    try {
        const { outcome, performanceScore } = req.body;
        
        if (!outcome || typeof performanceScore !== 'number') {
            return res.status(400).json({
                success: false,
                message: 'Invalid outcome or performance score'
            });
        }

        await database.updateSignalPerformance(req.params.id, outcome, performanceScore);

        res.json({
            success: true,
            message: 'Signal performance updated'
        });

    } catch (error) {
        console.error('Signal update error:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// ========== SOURCES ENDPOINTS ==========

// Get news sources
app.get('/api/sources', async (req, res) => {
    try {
        const sources = await database.getSources();

        res.json({
            success: true,
            data: sources,
            count: sources.length
        });

    } catch (error) {
        console.error('Sources fetch error:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// ========== ANALYTICS ENDPOINTS ==========

// Get analytics data
app.get('/api/analytics', async (req, res) => {
    try {
        const { category, hours = 24 } = req.query;
        const analytics = await database.getAnalytics(category, parseInt(hours));

        // Get basic stats
        const stats = await database.getStats();

        res.json({
            success: true,
            data: {
                metrics: analytics,
                stats: stats,
                category: category || 'all',
                timeframe: `${hours}h`
            }
        });

    } catch (error) {
        console.error('Analytics error:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Get dashboard data (combined endpoint for frontend)
app.get('/api/dashboard', async (req, res) => {
    try {
        const [
            recentArticles,
            dexData,
            activeSignals,
            stats,
            analytics
        ] = await Promise.allSettled([
            database.getRecentArticles(20),
            dexService.getDexData(),
            database.getActiveTradingSignals(),
            database.getStats(),
            database.getAnalytics(null, 24)
        ]);

        const dashboard = {
            articles: recentArticles.status === 'fulfilled' ? recentArticles.value : [],
            dexData: dexData.status === 'fulfilled' ? dexData.value : {},
            signals: activeSignals.status === 'fulfilled' ? activeSignals.value : [],
            stats: stats.status === 'fulfilled' ? stats.value : {},
            analytics: analytics.status === 'fulfilled' ? analytics.value : [],
            timestamp: new Date().toISOString()
        };

        res.json({
            success: true,
            data: dashboard
        });

    } catch (error) {
        console.error('Dashboard error:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// ========== ANALYSIS ENDPOINTS ==========

// Trigger manual analysis
app.post('/api/analysis/run', async (req, res) => {
    try {
        if (analyzer.getStatus().isRunning) {
            return res.status(409).json({
                success: false,
                message: 'Analysis pipeline is already running'
            });
        }

        // Start analysis asynchronously
        analyzer.runFullAnalysis().then(result => {
            console.log('Manual analysis completed:', result);
            // Broadcast result via WebSocket if connected
            if (wss) {
                wss.clients.forEach(client => {
                    if (client.readyState === WebSocket.OPEN) {
                        client.send(JSON.stringify({
                            type: 'analysis_complete',
                            data: result
                        }));
                    }
                });
            }
        }).catch(error => {
            console.error('Manual analysis error:', error);
        });

        res.json({
            success: true,
            message: 'Analysis pipeline started',
            status: analyzer.getStatus()
        });

    } catch (error) {
        console.error('Analysis trigger error:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Get analysis status
app.get('/api/analysis/status', (req, res) => {
    res.json({
        success: true,
        data: analyzer.getStatus()
    });
});

// ========== STATIC FILE SERVING ==========

// Serve frontend pages
// Root route redirect to dashboard
app.get('/', (req, res) => {
    res.redirect('/dashboard');
});

app.get('/dashboard', (req, res) => {
    res.sendFile(path.join(__dirname, 'pages/news_dashboard.html'));
});

app.get('/analysis', (req, res) => {
    res.sendFile(path.join(__dirname, 'pages/article_analysis.html'));
});

app.get('/sources', (req, res) => {
    res.sendFile(path.join(__dirname, 'pages/source_management.html'));
});

app.get('/search', (req, res) => {
    res.sendFile(path.join(__dirname, 'pages/search_results.html'));
});

// ========== WEBSOCKET SETUP ==========

let wss;

function setupWebSocket() {
    wss = new WebSocket.Server({ port: WS_PORT });

    wss.on('connection', (ws) => {
        console.log('WebSocket client connected');

        ws.send(JSON.stringify({
            type: 'connection',
            message: 'Connected to PulseChain News Hub',
            timestamp: new Date().toISOString()
        }));

        ws.on('message', (message) => {
            try {
                const data = JSON.parse(message);
                console.log('WebSocket message:', data);

                // Handle client requests
                switch (data.type) {
                    case 'subscribe':
                        ws.isSubscribed = true;
                        break;
                    case 'unsubscribe':
                        ws.isSubscribed = false;
                        break;
                }
            } catch (error) {
                console.error('WebSocket message error:', error);
            }
        });

        ws.on('close', () => {
            console.log('WebSocket client disconnected');
        });
    });

    console.log(`WebSocket server running on port ${WS_PORT}`);
}

// ========== CRON JOBS ==========

// Run analysis every 5 minutes
cron.schedule('*/5 * * * *', async () => {
    console.log('Running scheduled analysis...');
    try {
        if (!analyzer.getStatus().isRunning) {
            const result = await analyzer.runFullAnalysis();
            
            // Broadcast to connected WebSocket clients
            if (wss) {
                wss.clients.forEach(client => {
                    if (client.readyState === WebSocket.OPEN && client.isSubscribed) {
                        client.send(JSON.stringify({
                            type: 'scheduled_update',
                            data: result,
                            timestamp: new Date().toISOString()
                        }));
                    }
                });
            }
        }
    } catch (error) {
        console.error('Scheduled analysis error:', error);
    }
});

// Cleanup old data daily at 2 AM
cron.schedule('0 2 * * *', async () => {
    console.log('Running daily cleanup...');
    try {
        await analyzer.cleanup();
    } catch (error) {
        console.error('Daily cleanup error:', error);
    }
});

// ========== SERVER STARTUP ==========

async function startServer() {
    try {
        console.log('Starting PulseChain News Hub server...');

        // Initialize database
        await database.initialize();

        // Setup WebSocket server
        setupWebSocket();

        // Start HTTP server
        app.listen(PORT, () => {
            console.log(`HTTP server running on port ${PORT}`);
            console.log(`Dashboard: http://localhost:${PORT}/dashboard`);
            console.log(`API: http://localhost:${PORT}/api/health`);
        });

        // Run initial analysis
        console.log('Running initial analysis...');
        setTimeout(() => {
            analyzer.runFullAnalysis().then(result => {
                console.log('Initial analysis completed:', result.success ? 'Success' : 'Failed');
            }).catch(error => {
                console.error('Initial analysis error:', error.message);
            });
        }, 5000); // Wait 5 seconds for server to fully start

    } catch (error) {
        console.error('Server startup error:', error);
        process.exit(1);
    }
}

// Graceful shutdown
process.on('SIGINT', async () => {
    console.log('Shutting down gracefully...');
    
    if (wss) {
        wss.close();
    }
    
    await database.close();
    process.exit(0);
});

process.on('SIGTERM', async () => {
    console.log('SIGTERM received, shutting down...');
    await database.close();
    process.exit(0);
});

// Start the server
startServer();