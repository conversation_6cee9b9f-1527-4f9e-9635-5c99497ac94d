<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test</title>
</head>
<body>
    <h1>API Test Page</h1>
    <div id="status">Loading...</div>
    <div id="results"></div>
    
    <script src="js/api.js"></script>
    <script>
        console.log('Test page loaded');
        console.log('PulseChainAPI available:', typeof PulseChainAPI !== 'undefined');
        
        async function testAPI() {
            try {
                console.log('Creating API instance...');
                const api = new PulseChainAPI();
                console.log('API instance created');
                
                console.log('Testing health endpoint...');
                const health = await api.getHealth();
                console.log('Health response:', health);
                
                console.log('Testing dashboard endpoint...');
                const dashboard = await api.getDashboardData();
                console.log('Dashboard response:', dashboard);
                
                document.getElementById('status').textContent = 'API Test Successful!';
                document.getElementById('results').innerHTML = `
                    <h2>Health Status:</h2>
                    <pre>${JSON.stringify(health, null, 2)}</pre>
                    <h2>Dashboard Data:</h2>
                    <pre>${JSON.stringify(dashboard, null, 2)}</pre>
                `;
                
            } catch (error) {
                console.error('API test failed:', error);
                document.getElementById('status').textContent = 'API Test Failed: ' + error.message;
                document.getElementById('results').innerHTML = `<pre>${error.stack}</pre>`;
            }
        }
        
        // Wait for DOM and then test
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(testAPI, 100);
        });
    </script>
</body>
</html>
