<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Article Analysis - PulseChain News Hub</title>
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../css/tailwind.css">
    <style>
        .loading-spinner {
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        .sentiment-positive { color: var(--color-success); }
        .sentiment-negative { color: var(--color-error); }
        .sentiment-neutral { color: var(--color-text-secondary); }
        .progress-bar {
            transition: width 0.3s ease-in-out;
        }
        .analysis-badge {
            background: var(--color-primary-light);
            color: var(--color-primary-dark);
            padding: 0.25rem 0.75rem;
            border-radius: var(--border-radius);
            font-size: 0.875rem;
            font-weight: 600;
        }
    </style>
</head>
<body class="bg-surface min-h-screen">
    <!-- Header Navigation -->
    <header class="bg-surface border-b border-border sticky top-0 z-50 shadow-card">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between h-16">
                <!-- Logo -->
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-8 w-8 text-primary" viewBox="0 0 32 32" fill="currentColor">
                            <path d="M16 2L4 8v16l12 6 12-6V8L16 2zm0 4l8 4v12l-8 4-8-4V10l8-4z"/>
                            <circle cx="16" cy="16" r="4" fill="currentColor"/>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h1 class="text-xl font-bold text-text-primary">PulseChain News Hub</h1>
                    </div>
                </div>

                <!-- Navigation -->
                <nav class="hidden md:flex space-x-8">
                    <a href="/dashboard" class="text-text-secondary hover:text-primary px-3 py-2 rounded-md hover:bg-secondary-50 transition-colors">Dashboard</a>
                    <a href="/analysis" class="text-primary font-medium px-3 py-2 rounded-md bg-primary-50">Analysis</a>
                    <a href="/sources" class="text-text-secondary hover:text-primary px-3 py-2 rounded-md hover:bg-secondary-50 transition-colors">Sources</a>
                    <a href="/search" class="text-text-secondary hover:text-primary px-3 py-2 rounded-md hover:bg-secondary-50 transition-colors">Search</a>
                </nav>

                <!-- Mobile menu button -->
                <div class="md:hidden">
                    <button type="button" class="text-text-secondary hover:text-primary p-2" onclick="toggleMobileMenu()">
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Navigation -->
        <div id="mobile-menu" class="md:hidden hidden bg-surface border-t border-border">
            <div class="px-2 pt-2 pb-3 space-y-1">
                <a href="/dashboard" class="block px-3 py-2 text-text-secondary hover:text-primary hover:bg-secondary-50 rounded-md">Dashboard</a>
                <a href="/analysis" class="block px-3 py-2 text-primary font-medium bg-primary-50 rounded-md">Analysis</a>
                <a href="/sources" class="block px-3 py-2 text-text-secondary hover:text-primary hover:bg-secondary-50 rounded-md">Sources</a>
                <a href="/search" class="block px-3 py-2 text-text-secondary hover:text-primary hover:bg-secondary-50 rounded-md">Search</a>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-6 py-8">
        <!-- Loading State -->
        <div id="loading-state" class="text-center py-12">
            <div class="loading-spinner inline-block w-12 h-12 border-4 border-primary border-t-transparent rounded-full"></div>
            <p class="mt-4 text-text-secondary">Loading article analysis...</p>
        </div>

        <!-- Error State -->
        <div id="error-state" class="hidden alert alert-error mb-6">
            <h3 class="font-bold">Error</h3>
            <p id="error-message">Failed to load article</p>
            <button onclick="loadArticle()" class="mt-2 btn btn-error">
                Try Again
            </button>
        </div>

        <!-- Article Content -->
        <div id="article-content" class="hidden space-y-6">
            <!-- Article Header -->
            <div class="card">
                <div class="flex justify-between items-start mb-6">
                    <div class="flex-1">
                        <h1 id="article-title" class="text-3xl font-bold text-text-primary mb-3">Article Title</h1>
                        <div class="flex flex-wrap gap-4 text-sm">
                            <span class="analysis-badge">Source: <span id="article-source" class="font-semibold">Loading...</span></span>
                            <span class="analysis-badge">Published: <span id="article-date" class="font-semibold">Loading...</span></span>
                            <span class="analysis-badge">Credibility: <span id="article-credibility" class="font-semibold">Loading...</span>/10</span>
                        </div>
                    </div>
                    <a id="article-url" href="#" target="_blank" class="btn btn-primary ml-4">
                        View Original
                    </a>
                </div>
                
                <div id="article-description" class="text-text-primary text-lg leading-relaxed">
                    Loading article description...
                </div>
            </div>

            <!-- AI Analysis -->
            <div class="card">
                <h2 class="text-2xl font-bold text-text-primary mb-6 flex items-center gap-3">
                    <span class="text-primary">🤖</span> AI Analysis
                </h2>
                
                <!-- Sentiment Analysis -->
                <div class="mb-8">
                    <h3 class="section-header mb-4">Sentiment Analysis</h3>
                    <div class="bg-surface-alt rounded-lg p-4">
                        <div class="flex items-center space-x-4">
                            <div class="flex-1">
                                <div class="bg-border rounded-full h-3">
                                    <div id="sentiment-bar" class="progress-bar h-3 rounded-full bg-primary" style="width: 0%"></div>
                                </div>
                            </div>
                            <div class="text-right">
                                <div id="sentiment-label" class="font-bold text-xl">Loading...</div>
                                <div id="sentiment-score" class="text-sm text-text-secondary">Score: 0</div>
                                <div id="sentiment-confidence" class="text-xs text-text-muted">Confidence: 0%</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Trading Signal -->
                <div id="trading-signal-section" class="mb-8 hidden">
                    <h3 class="section-header mb-4">Trading Signal</h3>
                    <div class="bg-surface-alt rounded-lg p-6 border-l-4 border-primary">
                        <div class="flex justify-between items-center">
                            <div>
                                <div id="trading-signal" class="text-3xl font-bold mb-1">HOLD</div>
                                <div id="signal-timeframe" class="text-text-secondary">Timeframe: Medium</div>
                            </div>
                            <div class="text-right">
                                <div id="signal-confidence" class="text-2xl font-bold text-primary">0%</div>
                                <div class="text-xs text-text-muted">Confidence</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Key Insights -->
                <div id="key-insights-section" class="mb-8 hidden">
                    <h3 class="section-header mb-4">Key Insights</h3>
                    <div class="bg-surface-alt rounded-lg p-4">
                        <ul id="key-insights" class="space-y-3">
                            <!-- Insights will be populated here -->
                        </ul>
                    </div>
                </div>

                <!-- Price Impact -->
                <div id="price-impact-section" class="hidden">
                    <h3 class="section-header mb-4">Predicted Price Impact</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="metric-card">
                            <div class="metric-label">Short-term (1-7 days)</div>
                            <div id="price-impact-short" class="metric-value">0%</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-label">Long-term (1-4 weeks)</div>
                            <div id="price-impact-long" class="metric-value">0%</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Related Articles -->
            <div class="card">
                <h2 class="text-2xl font-bold text-text-primary mb-6">Related Articles</h2>
                <div id="related-articles" class="space-y-4">
                    <p class="text-text-secondary">Loading related articles...</p>
                </div>
            </div>
        </div>
    </main>

    <!-- Include API Client -->
    <script src="../js/api.js"></script>
    
    <script>
        // Mobile menu toggle
        function toggleMobileMenu() {
            const menu = document.getElementById('mobile-menu');
            menu.classList.toggle('hidden');
        }

        // Get article ID from URL
        const urlParams = new URLSearchParams(window.location.search);
        const articleId = urlParams.get('id');

        // DOM elements
        const loadingState = document.getElementById('loading-state');
        const errorState = document.getElementById('error-state');
        const articleContent = document.getElementById('article-content');
        const errorMessage = document.getElementById('error-message');

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Article analysis page initializing...');
            if (articleId) {
                loadArticle();
            } else {
                showError('No article ID provided');
            }
        });

        // Load article data
        async function loadArticle() {
            if (!articleId) {
                showError('No article ID provided');
                return;
            }

            try {
                console.log('Loading article:', articleId);
                
                showLoading();
                const response = await PulseChainAPI.getArticle(articleId);

                if (response.success) {
                    displayArticle(response.data);
                    hideLoading();
                } else {
                    throw new Error('Article not found');
                }

            } catch (error) {
                console.error('Article loading error:', error);
                showError('Failed to load article: ' + error.message);
            }
        }

        // Display article
        function displayArticle(article) {
            // Basic article info
            document.getElementById('article-title').textContent = article.title;
            document.getElementById('article-source').textContent = article.source_name || 'Unknown';
            document.getElementById('article-date').textContent = new Date(article.published_at).toLocaleDateString();
            document.getElementById('article-credibility').textContent = (article.source_credibility || 0).toFixed(1);
            document.getElementById('article-description').textContent = article.description || 'No description available';
            
            if (article.url) {
                document.getElementById('article-url').href = article.url;
            }

            // Sentiment Analysis
            displaySentiment(article);

            // Trading Signal
            if (article.trading_signal) {
                displayTradingSignal(article);
            }

            // Key Insights
            if (article.key_insights) {
                displayKeyInsights(article.key_insights);
            }

            // Price Impact
            if (article.price_impact_short || article.price_impact_long) {
                displayPriceImpact(article);
            }

            // Load related articles (simplified)
            loadRelatedArticles(article.sentiment_label);
        }

        // Display sentiment analysis
        function displaySentiment(article) {
            const sentimentLabel = article.sentiment_label || 'neutral';
            const sentimentScore = article.sentiment_score || 0;
            const sentimentConfidence = article.sentiment_confidence || 0;

            document.getElementById('sentiment-label').textContent = sentimentLabel.toUpperCase();
            document.getElementById('sentiment-score').textContent = `Score: ${sentimentScore.toFixed(2)}`;
            document.getElementById('sentiment-confidence').textContent = `Confidence: ${Math.round(sentimentConfidence * 100)}%`;

            // Update progress bar and color
            const sentimentBar = document.getElementById('sentiment-bar');
            const normalizedScore = Math.abs(sentimentScore) * 100;
            sentimentBar.style.width = `${Math.min(normalizedScore, 100)}%`;

            // Set color based on sentiment
            const labelElement = document.getElementById('sentiment-label');
            if (sentimentLabel === 'positive') {
                labelElement.className = 'font-bold text-xl sentiment-positive';
                sentimentBar.style.backgroundColor = 'var(--color-success)';
            } else if (sentimentLabel === 'negative') {
                labelElement.className = 'font-bold text-xl sentiment-negative';
                sentimentBar.style.backgroundColor = 'var(--color-error)';
            } else {
                labelElement.className = 'font-bold text-xl sentiment-neutral';
                sentimentBar.style.backgroundColor = 'var(--color-text-secondary)';
            }
        }

        // Display trading signal
        function displayTradingSignal(article) {
            const section = document.getElementById('trading-signal-section');
            section.classList.remove('hidden');

            document.getElementById('trading-signal').textContent = article.trading_signal || 'HOLD';
            document.getElementById('signal-timeframe').textContent = `Timeframe: ${article.signal_timeframe || 'Medium'}`;
            document.getElementById('signal-confidence').textContent = `${Math.round((article.signal_confidence || 0.5) * 100)}%`;

            // Set signal color
            const signalElement = document.getElementById('trading-signal');
            const signal = article.trading_signal;
            if (signal === 'BUY') {
                signalElement.className = 'text-3xl font-bold mb-1';
                signalElement.style.color = 'var(--color-success)';
            } else if (signal === 'SELL') {
                signalElement.className = 'text-3xl font-bold mb-1';
                signalElement.style.color = 'var(--color-error)';
            } else {
                signalElement.className = 'text-3xl font-bold mb-1';
                signalElement.style.color = 'var(--color-warning)';
            }
        }

        // Display key insights
        function displayKeyInsights(insights) {
            const section = document.getElementById('key-insights-section');
            const container = document.getElementById('key-insights');

            if (!insights || insights.length === 0) return;

            section.classList.remove('hidden');

            // Parse insights if it's a JSON string
            let parsedInsights = insights;
            if (typeof insights === 'string') {
                try {
                    parsedInsights = JSON.parse(insights);
                } catch (e) {
                    parsedInsights = [insights];
                }
            }

            container.innerHTML = parsedInsights.map(insight => 
                `<li class="flex items-start space-x-3 p-3 bg-surface rounded-md border border-border">
                    <span class="text-primary font-bold text-lg">•</span>
                    <span class="text-text-primary flex-1">${insight}</span>
                </li>`
            ).join('');
        }

        // Display price impact
        function displayPriceImpact(article) {
            const section = document.getElementById('price-impact-section');
            section.classList.remove('hidden');

            const shortImpact = article.price_impact_short || 0;
            const longImpact = article.price_impact_long || 0;

            document.getElementById('price-impact-short').textContent = `${shortImpact > 0 ? '+' : ''}${shortImpact.toFixed(2)}%`;
            document.getElementById('price-impact-long').textContent = `${longImpact > 0 ? '+' : ''}${longImpact.toFixed(2)}%`;

            // Color code the impacts
            const shortElement = document.getElementById('price-impact-short');
            const longElement = document.getElementById('price-impact-long');

            shortElement.style.color = shortImpact >= 0 ? 'var(--color-success)' : 'var(--color-error)';
            longElement.style.color = longImpact >= 0 ? 'var(--color-success)' : 'var(--color-error)';
        }

        // Load related articles
        async function loadRelatedArticles(sentiment) {
            try {
                const response = await PulseChainAPI.getArticles({ limit: 5, sentiment });
                const container = document.getElementById('related-articles');

                if (response.success && response.data.length > 0) {
                    const relatedHtml = response.data.map(article => `
                        <div class="article-card border-l-4 border-primary pl-4 py-3 bg-surface-alt rounded-r-md">
                            <h4 class="font-semibold text-text-primary mb-1">
                                <a href="/analysis?id=${article.id}" class="hover:text-primary transition-colors">
                                    ${article.title}
                                </a>
                            </h4>
                            <p class="text-sm text-text-secondary">${article.source_name} • ${new Date(article.published_at).toLocaleDateString()}</p>
                        </div>
                    `).join('');
                    container.innerHTML = relatedHtml;
                } else {
                    container.innerHTML = '<p class="text-text-secondary">No related articles found</p>';
                }
            } catch (error) {
                console.error('Related articles error:', error);
                document.getElementById('related-articles').innerHTML = '<p class="text-text-secondary">Failed to load related articles</p>';
            }
        }

        // Show loading state
        function showLoading() {
            loadingState.classList.remove('hidden');
            errorState.classList.add('hidden');
            articleContent.classList.add('hidden');
        }

        // Hide loading state
        function hideLoading() {
            loadingState.classList.add('hidden');
            articleContent.classList.remove('hidden');
        }

        // Show error state
        function showError(message) {
            errorMessage.textContent = message;
            loadingState.classList.add('hidden');
            errorState.classList.remove('hidden');
            articleContent.classList.add('hidden');
        }
    </script>
</body>
</html>