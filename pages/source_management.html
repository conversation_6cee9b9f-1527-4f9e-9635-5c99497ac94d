<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Source Management - PulseChain News Hub</title>
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../css/tailwind.css">
    <style>
        .loading-spinner {
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        .source-card {
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        .source-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body class="bg-background min-h-screen">
    <!-- Header Navigation -->
    <header class="bg-surface border-b border-border sticky top-0 z-50 shadow-card">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between h-16">
                <!-- Logo -->
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-8 w-8 text-primary" viewBox="0 0 32 32" fill="currentColor">
                            <path d="M16 2L4 8v16l12 6 12-6V8L16 2zm0 4l8 4v12l-8 4-8-4V10l8-4z"/>
                            <circle cx="16" cy="16" r="4" fill="currentColor"/>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h1 class="text-xl font-bold text-text-primary">PulseChain News Hub</h1>
                    </div>
                </div>

                <!-- Navigation -->
                <nav class="hidden md:flex space-x-8">
                    <a href="/dashboard" class="text-text-secondary hover:text-primary px-3 py-2 rounded-md hover:bg-secondary-50 transition-colors">Dashboard</a>
                    <a href="/analysis" class="text-text-secondary hover:text-primary px-3 py-2 rounded-md hover:bg-secondary-50 transition-colors">Analysis</a>
                    <a href="/sources" class="text-primary font-medium px-3 py-2 rounded-md bg-primary-50">Sources</a>
                    <a href="/search" class="text-text-secondary hover:text-primary px-3 py-2 rounded-md hover:bg-secondary-50 transition-colors">Search</a>
                </nav>

                <!-- Mobile menu button -->
                <div class="md:hidden">
                    <button type="button" class="text-text-secondary hover:text-primary p-2" onclick="toggleMobileMenu()">
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Navigation -->
        <div id="mobile-menu" class="md:hidden hidden bg-surface border-t border-border">
            <div class="px-2 pt-2 pb-3 space-y-1">
                <a href="/dashboard" class="block px-3 py-2 text-text-secondary hover:text-primary hover:bg-secondary-50 rounded-md">Dashboard</a>
                <a href="/analysis" class="block px-3 py-2 text-text-secondary hover:text-primary hover:bg-secondary-50 rounded-md">Analysis</a>
                <a href="/sources" class="block px-3 py-2 text-primary font-medium bg-primary-50 rounded-md">Sources</a>
                <a href="/search" class="block px-3 py-2 text-text-secondary hover:text-primary hover:bg-secondary-50 rounded-md">Search</a>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <!-- Page Header -->
        <div class="mb-6">
            <div class="bg-surface rounded-lg shadow-card p-6 border border-border">
                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                    <div>
                        <h1 class="text-2xl font-bold text-text-primary mb-2">Source Management</h1>
                        <p class="text-text-secondary">Manage and monitor your PulseChain news sources</p>
                    </div>
                    <div class="flex items-center gap-3">
                        <button id="refresh-btn" class="btn-primary">
                            <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                            </svg>
                            Refresh Sources
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Stats Summary -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <div class="bg-surface rounded-lg shadow-card p-6 border border-border">
                <div class="text-center">
                    <div class="text-2xl font-bold text-primary" id="total-sources">Loading...</div>
                    <div class="text-sm text-text-secondary">Total Sources</div>
                </div>
            </div>
            <div class="bg-surface rounded-lg shadow-card p-6 border border-border">
                <div class="text-center">
                    <div class="text-2xl font-bold text-success-600" id="active-sources">Loading...</div>
                    <div class="text-sm text-text-secondary">Active Sources</div>
                </div>
            </div>
            <div class="bg-surface rounded-lg shadow-card p-6 border border-border">
                <div class="text-center">
                    <div class="text-2xl font-bold text-accent-600" id="avg-credibility">Loading...</div>
                    <div class="text-sm text-text-secondary">Avg. Credibility</div>
                </div>
            </div>
            <div class="bg-surface rounded-lg shadow-card p-6 border border-border">
                <div class="text-center">
                    <div class="text-2xl font-bold text-secondary-600" id="total-articles">Loading...</div>
                    <div class="text-sm text-text-secondary">Articles Scraped</div>
                </div>
            </div>
        </div>

        <!-- Sources List -->
        <div class="bg-white rounded-lg shadow">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Source Status</h3>
            </div>
            
            <!-- Loading State -->
            <div id="loading-state" class="text-center py-12">
                <div class="loading-spinner inline-block w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full"></div>
                <p class="mt-2 text-gray-600">Loading sources...</p>
            </div>

            <!-- Sources Container -->
            <div id="sources-container" class="hidden">
                <!-- Sources will be populated here -->
            </div>

            <!-- Error State -->
            <div id="error-state" class="hidden p-6 text-center">
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                    <h3 class="font-bold">Error</h3>
                    <p id="error-message">Failed to load sources</p>
                    <button onclick="loadSources()" class="mt-2 bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700">
                        Try Again
                    </button>
                </div>
            </div>
        </div>
    </main>

    <!-- Include API Client -->
    <script src="../js/api.js"></script>
    
    <script>
        // Mobile menu toggle
        function toggleMobileMenu() {
            const menu = document.getElementById('mobile-menu');
            menu.classList.toggle('hidden');
        }

        // DOM elements
        const loadingState = document.getElementById('loading-state');
        const sourcesContainer = document.getElementById('sources-container');
        const errorState = document.getElementById('error-state');
        const errorMessage = document.getElementById('error-message');
        const refreshBtn = document.getElementById('refresh-btn');

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Sources page initializing...');
            loadSources();
            setupEventListeners();
        });

        // Setup event listeners
        function setupEventListeners() {
            refreshBtn.addEventListener('click', loadSources);
        }

        // Load sources data
        async function loadSources() {
            try {
                console.log('Loading sources...');
                showLoading();
                
                const response = await PulseChainAPI.getSources();

                if (response.success) {
                    displaySources(response.data);
                    updateStats(response.data);
                    hideLoading();
                } else {
                    throw new Error('Failed to load sources');
                }

            } catch (error) {
                console.error('Sources loading error:', error);
                showError('Failed to load sources: ' + error.message);
            }
        }

        // Display sources
        function displaySources(sources) {
            if (!sources || sources.length === 0) {
                sourcesContainer.innerHTML = `
                    <div class="p-6 text-center">
                        <p class="text-gray-600">No sources available</p>
                    </div>
                `;
                return;
            }

            const sourceElements = sources.map(source => createSourceElement(source)).join('');
            sourcesContainer.innerHTML = sourceElements;
        }

        // Create source element
        function createSourceElement(source) {
            // Format last scraped date
            const lastScraped = source.last_scraped_at 
                ? new Date(source.last_scraped_at).toLocaleString()
                : 'Never';

            // Get status color
            const isActive = source.is_active;
            const statusColor = isActive ? 'text-green-600 bg-green-100' : 'text-red-600 bg-red-100';
            const statusText = isActive ? 'Active' : 'Inactive';

            // Get credibility color
            const credibility = parseFloat(source.credibility_score) || 0;
            let credibilityColor = 'text-gray-600';
            if (credibility >= 8) credibilityColor = 'text-green-600';
            else if (credibility >= 6) credibilityColor = 'text-yellow-600';
            else if (credibility >= 4) credibilityColor = 'text-orange-600';
            else credibilityColor = 'text-red-600';

            return `
                <div class="source-card border-b border-gray-200 p-6">
                    <div class="flex justify-between items-start">
                        <div class="flex-1">
                            <div class="flex items-center justify-between mb-2">
                                <h4 class="text-lg font-semibold text-gray-900">${source.name}</h4>
                                <span class="inline-flex px-2 py-1 text-xs font-medium rounded-full ${statusColor}">
                                    ${statusText}
                                </span>
                            </div>
                            
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                                <div>
                                    <span class="text-gray-600">Type: </span>
                                    <span class="font-medium capitalize">${source.type}</span>
                                </div>
                                <div>
                                    <span class="text-gray-600">Credibility: </span>
                                    <span class="font-medium ${credibilityColor}">${credibility.toFixed(1)}/10</span>
                                </div>
                                <div>
                                    <span class="text-gray-600">Articles: </span>
                                    <span class="font-medium">${source.total_articles || 0}</span>
                                </div>
                            </div>
                            
                            <div class="mt-2 text-sm">
                                <span class="text-gray-600">Last Scraped: </span>
                                <span class="font-medium">${lastScraped}</span>
                            </div>
                            
                            <div class="mt-2">
                                <a href="${source.url}" target="_blank" class="text-blue-600 hover:text-blue-800 text-sm">
                                    ${source.url}
                                    <svg class="inline w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"/>
                                    </svg>
                                </a>
                            </div>

                            ${source.tags ? `
                            <div class="mt-3">
                                <div class="flex flex-wrap gap-1">
                                    ${JSON.parse(source.tags).map(tag => 
                                        `<span class="inline-flex px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded">${tag}</span>`
                                    ).join('')}
                                </div>
                            </div>
                            ` : ''}
                        </div>
                        
                        <div class="ml-4 flex flex-col space-y-2">
                            <button onclick="viewSourceArticles(${source.id})" 
                                    class="bg-blue-600 text-white px-3 py-1 text-xs rounded hover:bg-blue-700">
                                View Articles
                            </button>
                            ${source.success_rate !== undefined ? `
                            <div class="text-xs text-center">
                                <div class="text-gray-600">Success Rate</div>
                                <div class="font-bold ${source.success_rate > 0.8 ? 'text-green-600' : source.success_rate > 0.5 ? 'text-yellow-600' : 'text-red-600'}">
                                    ${Math.round(source.success_rate * 100)}%
                                </div>
                            </div>
                            ` : ''}
                        </div>
                    </div>
                </div>
            `;
        }

        // Update stats
        function updateStats(sources) {
            const totalSources = sources.length;
            const activeSources = sources.filter(s => s.is_active).length;
            const avgCredibility = sources.reduce((sum, s) => sum + (parseFloat(s.credibility_score) || 0), 0) / totalSources;
            const totalArticles = sources.reduce((sum, s) => sum + (s.total_articles || 0), 0);

            document.getElementById('total-sources').textContent = totalSources;
            document.getElementById('active-sources').textContent = activeSources;
            document.getElementById('avg-credibility').textContent = avgCredibility.toFixed(1);
            document.getElementById('total-articles').textContent = totalArticles;
        }

        // View source articles
        function viewSourceArticles(sourceId) {
            // For now, redirect to dashboard with source filter
            // In a more advanced version, you could filter by source
            window.location.href = '/dashboard';
        }

        // Show loading state
        function showLoading() {
            loadingState.classList.remove('hidden');
            sourcesContainer.classList.add('hidden');
            errorState.classList.add('hidden');
        }

        // Hide loading state
        function hideLoading() {
            loadingState.classList.add('hidden');
            sourcesContainer.classList.remove('hidden');
        }

        // Show error state
        function showError(message) {
            errorMessage.textContent = message;
            loadingState.classList.add('hidden');
            sourcesContainer.classList.add('hidden');
            errorState.classList.remove('hidden');
        }
    </script>
</body>
</html>