<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Dashboard Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .loading { background-color: #d1ecf1; color: #0c5460; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Simple Dashboard Test</h1>
    <div id="status" class="status loading">Loading...</div>
    <div id="results"></div>
    
    <script src="js/api.js"></script>
    <script>
        console.log('Simple dashboard loaded');
        console.log('PulseChainAPI available:', typeof PulseChainAPI !== 'undefined');
        
        const statusDiv = document.getElementById('status');
        const resultsDiv = document.getElementById('results');
        
        async function testDashboard() {
            try {
                statusDiv.textContent = 'Initializing API...';
                statusDiv.className = 'status loading';
                
                if (typeof PulseChainAPI === 'undefined') {
                    throw new Error('PulseChainAPI class not found');
                }
                
                console.log('Creating API instance...');
                const api = new PulseChainAPI();
                console.log('API instance created');
                
                statusDiv.textContent = 'Testing API health...';
                const health = await api.getHealth();
                console.log('Health response:', health);
                
                statusDiv.textContent = 'Loading dashboard data...';
                const dashboard = await api.getDashboardData();
                console.log('Dashboard response:', dashboard);
                
                if (dashboard.success) {
                    statusDiv.textContent = `Dashboard loaded successfully! Found ${dashboard.data.articles.length} articles`;
                    statusDiv.className = 'status success';
                    
                    resultsDiv.innerHTML = `
                        <h2>Articles (${dashboard.data.articles.length})</h2>
                        <div>
                            ${dashboard.data.articles.slice(0, 5).map(article => `
                                <div style="border: 1px solid #ddd; margin: 10px 0; padding: 10px; border-radius: 5px;">
                                    <h3>${article.title}</h3>
                                    <p><strong>Source:</strong> ${article.source_name}</p>
                                    <p><strong>Published:</strong> ${new Date(article.published_at).toLocaleString()}</p>
                                    <p>${article.description || 'No description'}</p>
                                </div>
                            `).join('')}
                        </div>
                        
                        <h2>Stats</h2>
                        <pre>${JSON.stringify(dashboard.data.stats, null, 2)}</pre>
                        
                        <h2>DEX Data</h2>
                        <pre>${JSON.stringify(dashboard.data.dexData, null, 2)}</pre>
                    `;
                } else {
                    throw new Error('Dashboard API returned success: false');
                }
                
            } catch (error) {
                console.error('Dashboard test failed:', error);
                statusDiv.textContent = 'Error: ' + error.message;
                statusDiv.className = 'status error';
                resultsDiv.innerHTML = `<pre>${error.stack}</pre>`;
            }
        }
        
        // Wait for DOM and then test
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(testDashboard, 500);
        });
    </script>
</body>
</html>
